package com.device_manager

import android.content.Context
import android.os.BatteryManager
import android.os.StatFs
import android.os.Environment
import android.telephony.CellInfo
import android.telephony.CellInfoLte
import android.telephony.TelephonyManager
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
  private val CHANNEL = "com.device_manager/device"

  override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
    super.configureFlutterEngine(flutterEngine)

    MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
      when (call.method) {
        "getSignalStrength" -> {
          val tm = getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
          val cellInfoList = tm.allCellInfo
          var strength: Int? = null
          if (cellInfoList != null) {
            for (ci in cellInfoList) {
              if (ci is CellInfoLte) {
                strength = ci.cellSignalStrength.dbm
                break
              }
            }
          }
          result.success(strength)
        }
        "getDeviceTemperature" -> {
          val bm = getSystemService(Context.BATTERY_SERVICE) as BatteryManager
          // This returns in tenths of a degree Celsius on many Android devices
          val tempInt = bm.getIntProperty(BatteryManager.BATTERY_PROPERTY_TEMPERATURE)
          // tempInt is in tenths of a degree, convert to double Celsius
          val temp = tempInt / 10.0
          result.success(temp)
        }
        "getStorageStats" -> {
          val statFs = StatFs(Environment.getDataDirectory().path)
          val blockSize = statFs.blockSizeLong
          val totalBlocks = statFs.blockCountLong
          val freeBlocks = statFs.availableBlocksLong
          val total = totalBlocks * blockSize
          val free = freeBlocks * blockSize
          val map = HashMap<String, Any>()
          map["total"] = total
          map["free"] = free
          result.success(map)
        }
        else -> result.notImplemented()
      }
    }
  }
}


