import Flutter
import UIKit

public class StorageInfoPlugin: NSObject, FlutterPlugin {
  public static func register(with registrar: FlutterPluginRegistrar) {
    let channel = FlutterMethodChannel(name: "com.anovahub/storage", binaryMessenger: registrar.messenger())
    let instance = StorageInfoPlugin()
    registrar.addMethodCallDelegate(instance, channel: channel)
  }

  public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
    if call.method == "getStorageStats" {
      if let attrs = try? FileManager.default.attributesOfFileSystem(forPath: NSHomeDirectory()),
         let total = attrs[.systemSize] as? NSNumber,
         let free = attrs[.systemFreeSize] as? NSNumber {
        result([
          "disk_size": total.int64Value,
          "disk_free": free.int64Value
        ])
      } else {
        result(FlutterError(code: "UNAV<PERSON><PERSON><PERSON><PERSON>", message: "Disk info not available", details: nil))
      }
    } else {
      result(FlutterMethodNotImplemented)
    }
  }
}
