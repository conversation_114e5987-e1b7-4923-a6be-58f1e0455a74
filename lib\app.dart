import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'routes/app_pages.dart';
import 'shared/theme/app_theme.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Smart Device Monitor',
      debugShowCheckedModeBanner: false,
      initialRoute: '/',
      getPages: routes,
      theme: AppTheme.lightTheme,
    );
  }
}