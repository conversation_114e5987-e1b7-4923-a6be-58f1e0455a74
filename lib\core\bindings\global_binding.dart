import 'package:get/get.dart';
import '../storage/secure_storage_service.dart';
import '../network/dio_client.dart';

class GlobalBinding extends Bindings {
  @override
  void dependencies() {
    // Core services as singletons
    Get.put<SecureStorageService>(SecureStorageService(), permanent: true);
    Get.put<DioClient>(
      DioClient(Get.find<SecureStorageService>()), 
      permanent: true
    );
  }
}