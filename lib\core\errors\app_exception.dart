class AppException implements Exception {
  final String message;
  AppException(this.message);

  @override
  String toString() => message;
}

class NetworkException extends AppException {
  NetworkException([super.message = "No internet connection"]);
}

class ServerException extends AppException {
  ServerException([super.message = "Server error occurred"]);
}

class ValidationException extends AppException {
  ValidationException([super.message = "Invalid request"]);
}

class UnknownException extends AppException {
  UnknownException([super.message = "Something went wrong"]);
}
