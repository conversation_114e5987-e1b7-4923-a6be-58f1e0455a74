import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'app_exception.dart';

class ErrorHandler {
  static void handle(Object error, {VoidCallback? retry}) {
    String message;

    if (error is AppException) {
      message = error.message;
    } else {
      message = "Unexpected error occurred";
    }

    // Show custom snackbar
    Get.snackbar(
      "Oops!",
      message,
      backgroundColor: Colors.redAccent,
      colorText: Colors.white,
      snackPosition: SnackPosition.BOTTOM,
      margin: const EdgeInsets.all(12),
      borderRadius: 12,
      duration: const Duration(seconds: 3),
      icon: const Icon(Icons.error_outline, color: Colors.white),
    );

    // Optional: retry dialog for critical errors
    if (retry != null) {
      Get.defaultDialog(
        title: "Error",
        middleText: message,
        textCancel: "Dismiss",
        textConfirm: "Retry",
        confirmTextColor: Colors.white,
        onConfirm: retry,
      );
    }

    // Optional: log error silently to Sentry/Firebase Crashlytics
    // AppLogger.log(error);
  }
}
