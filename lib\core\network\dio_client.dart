import 'package:dio/dio.dart';
import '../config/env.dart';
import 'interceptors/app_interceptor.dart';
import 'interceptors/auth_interceptor.dart';
import 'interceptors/logging_interceptor.dart';
import '../storage/secure_storage_service.dart';

class DioClient {
  final Dio dio;

  DioClient._internal(this.dio);

  factory DioClient(SecureStorageService storage) {
    final options = BaseOptions(
      baseUrl: Env.baseUrl,
      connectTimeout: Duration(milliseconds: Env.connectTimeoutMs),
      receiveTimeout: Duration(milliseconds: Env.receiveTimeoutMs),
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    );

    final dio = Dio(options);

    // Add interceptors
    dio.interceptors.addAll([
      AuthInterceptor(storage),
      AppInterceptor(),
      LoggingInterceptor(),
    ]);

    return DioClient._internal(dio);
  }
}