import 'package:dio/dio.dart';

import '../../errors/app_exception.dart';

class AppInterceptor extends Interceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    AppException appException;

    if (err.type == DioExceptionType.connectionError) {
      appException = NetworkException();
    } else if (err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        err.type == DioExceptionType.sendTimeout) {
      appException = NetworkException("Connection timeout, please try again.");
    } else if (err.response != null) {
      final statusCode = err.response?.statusCode;
      final serverMessage = err.response?.data?["MESSAGE"] ?? err.message;

      if (statusCode == 422) {
        appException = ValidationException(serverMessage ?? "Validation failed");
      } else if (statusCode != null && statusCode >= 500) {
        appException = ServerException("Server error occurred");
      } else {
        appException = UnknownException(serverMessage ?? "Unexpected error occurred");
      }
    } else {
      appException = UnknownException(err.message ?? "Unexpected error occurred");
    }

    // Pass the AppException forward instead of DioException
    handler.reject(
      DioException(
        requestOptions: err.requestOptions,
        error: appException,
      ),
    );
  }
}
