import 'package:dio/dio.dart';
import '../../storage/secure_storage_service.dart';
import 'package:get/get.dart';
import '../../../modules/auth/controllers/auth_controller.dart';

class AuthInterceptor extends Interceptor {
  final SecureStorageService storage;

  AuthInterceptor(this.storage);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    final token = await storage.readToken();
    if (token != null && token.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    return handler.next(options);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // If server responds 401, clear token and redirect to login
    if (err.response?.statusCode == 401) {
      try {
        final auth = Get.find<AuthController>();
        auth.handleUnauthorized(); 
      } catch (_) {
        // ignore if AuthController is not registered
      }
    }
    return handler.next(err);
  }
}