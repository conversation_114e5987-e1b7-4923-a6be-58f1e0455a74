import 'package:battery_plus/battery_plus.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:system_info2/system_info2.dart';
import 'platform_channels.dart';
import '../../features/device_report/data/models/device_report.dart';

class DeviceReportHelper {
  static Future<DeviceReport> collect(String deviceId) async {
    // Battery
    final battery = Battery();
    final batteryLevel = await battery.batteryLevel;

    // Connectivity type
    final conn = await Connectivity().checkConnectivity();
    String signalStatus = conn.toString(); // e.g. ConnectivityResult.wifi

    // Signal strength via native
    final int signalStrength = await PlatformChannels.getSignalStrength() ?? 0;

    // Temperature via native
    final double temperature = await PlatformChannels.getDeviceTemperature() ?? 0.0;

    // Storage stats
    final storageMap = await PlatformChannels.getStorageStats();
    final int storageTotal = (storageMap["total"] as int?) ?? 0;
    final int storageFree = (storageMap["free"] as int?) ?? 0;

    // RAM
    final ramTotal = SysInfo.getTotalPhysicalMemory();
    final ramFree = SysInfo.getFreePhysicalMemory();

    return DeviceReport(
      deviceId: deviceId,
      signalStrength: signalStrength,
      batteryLevel: batteryLevel,
      temperature: temperature,
      signalStatus: signalStatus,
      storageTotal: storageTotal,
      storageFree: storageFree,
      ramTotal: ramTotal,
      ramFree: ramFree,
    );
  }
}
