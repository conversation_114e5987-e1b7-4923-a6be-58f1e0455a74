import 'package:flutter/services.dart';

class PlatformChannels {
  static const MethodChannel _channel = MethodChannel("com.device_manager/device");

  /// Get cellular / WiFi signal strength in dBm (Android)
  static Future<int?> getSignalStrength() async {
    try {
      final result = await _channel.invokeMethod<int>("getSignalStrength");
      return result;
    } catch (_) {
      return null;
    }
  }

  /// Get device temperature (battery sensor or system) (Android)
  static Future<double?> getDeviceTemperature() async {
    try {
      final result = await _channel.invokeMethod<double>("getDeviceTemperature");
      return result;
    } catch (_) {
      return null;
    }
  }

  /// Get storage stats: total and free
  static Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final result = await _channel.invokeMapMethod<String, dynamic>("getStorageStats");
      if (result == null) {
        return {"total": 0, "free": 0};
      }
      return result;
    } catch (_) {
      return {"total": 0, "free": 0};
    }
  }
}
