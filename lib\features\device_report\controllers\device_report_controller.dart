import 'package:get/get.dart';
import '../../../core/utils/device_report_helper.dart';
import '../data/repositories/device_report_repository.dart';
import '../data/models/device_report.dart';

class DeviceReportController extends GetxController {
  final DeviceReportRepository repository;

  DeviceReportController({required this.repository});

  final isLoading = false.obs;
  final error = "".obs;

  Future<void> submitReport(String deviceId) async {
    isLoading.value = true;
    error.value = "";

    try {
      DeviceReport report = await DeviceReportHelper.collect(deviceId);
      print(report.toJson());
      await repository.sendReport(report);
    } catch (e) {
      error.value = e.toString(); 
      // you might want to convert e to a user-friendly message
    } finally {
      isLoading.value = false;
    }
  }
}
