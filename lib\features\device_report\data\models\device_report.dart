class DeviceReport {
  final String deviceId;
  final int signalStrength;
  final int batteryLevel;
  final double temperature;
  final String signalStatus;
  final int storageTotal;
  final int storageFree;
  final int ramTotal;
  final int ramFree;

  DeviceReport({
    required this.deviceId,
    required this.signalStrength,
    required this.batteryLevel,
    required this.temperature,
    required this.signalStatus,
    required this.storageTotal,
    required this.storageFree,
    required this.ramTotal,
    required this.ramFree,
  });

  Map<String, dynamic> toJson() {
    return {
      "device_id": deviceId,
      "signal_strength": signalStrength,
      "battery_level": batteryLevel,
      "temperature": temperature,
      "signal_status": signalStatus,
      "disk_size": storageTotal,
      "disk_free": storageFree,
      "ram_size": ramTotal,
      "ram_free": ramFree,
    };
  }
}
