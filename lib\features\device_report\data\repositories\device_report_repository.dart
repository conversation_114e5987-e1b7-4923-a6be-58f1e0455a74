import '../../../../core/network/dio_client.dart';
import '../models/device_report.dart';

class DeviceReportRepository {
  final DioClient dioClient;

  DeviceReportRepository(this.dioClient);

  Future<void> sendReport(DeviceReport report) async {
    final response = await dioClient.dio.post("/app/device/report", data: report.toJson());
    if (response.statusCode != 200) {
      throw Exception("Failed to send device report");
    }
  }
}
