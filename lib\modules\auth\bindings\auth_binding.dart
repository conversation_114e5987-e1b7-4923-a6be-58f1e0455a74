import 'package:get/get.dart';
import '../../../core/storage/secure_storage_service.dart';
import '../../../core/network/dio_client.dart';
import '../data/repositories/auth_repository.dart';
import '../controllers/auth_controller.dart';

class AuthBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AuthRepository>(() => AuthRepository(Get.find<DioClient>()));
    Get.lazyPut<AuthController>(() => AuthController(
      repository: Get.find<AuthRepository>(),
      secureStorage: Get.find<SecureStorageService>(),
    ));
  }
}
