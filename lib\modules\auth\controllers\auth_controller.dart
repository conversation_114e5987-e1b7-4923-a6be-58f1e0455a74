import 'package:get/get.dart';
import '../data/repositories/auth_repository.dart';
import '../../../core/storage/secure_storage_service.dart';
import '../data/models/user_model.dart';
import '../../../core/errors/api_exception.dart';

class AuthController extends GetxController {
  final AuthRepository repository;
  final SecureStorageService secureStorage;

  AuthController({
    required this.repository,
    required this.secureStorage,
  });

  // Reactive state
  final Rxn<UserModel> _user = Rxn<UserModel>();
  final RxBool isLoading = false.obs;
  final RxnString errorMessage = RxnString();

  String? _token;

  // Getters
  UserModel? get user => _user.value;
  String? get token => _token;
  bool get isAuthenticated => _token != null && _token!.isNotEmpty;

  @override
  void onInit() {
    super.onInit();
    _loadTokenFromStorage();
  }

  Future<void> _loadTokenFromStorage() async {
    final token = await secureStorage.readToken();
    if (token != null && token.isNotEmpty) {
      _token = token;
      try {
        final profile = await repository.fetchProfile();
        _user.value = profile;
      } catch (_) {
        await logout();
      }
    }
  }

  Future<void> login(String email, String password) async {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      final data = await repository.login(email, password);

      final token = data['token'] as String? ?? data['access_token'] as String?;
      final userJson = data['user'] as Map<String, dynamic>? ??
          data['data'] as Map<String, dynamic>?;

      if (token == null) throw ApiException('Token missing in response');

      _token = token;
      await secureStorage.writeToken(token);

      if (userJson != null) {
        _user.value = UserModel.fromJson(userJson);
      } else {
        _user.value = await repository.fetchProfile();
      }

      // redirect after login
      Get.offAllNamed('/home');
    } on ApiException catch (e) {
      errorMessage.value = e.message;
      rethrow;
    } catch (e) {
      errorMessage.value = 'Something went wrong';
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> register({
    required String firstName,
    required String lastName,
    required String email,
    required String phone,
    required String password,
    required String passwordConfirmation,
  }) async {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      final data = await repository.register(
        firstName: firstName,
        lastName: lastName,
        email: email,
        phone: phone,
        password: password,
        passwordConfirmation: passwordConfirmation,
      );

      final token = data['token'] as String? ?? data['access_token'] as String?;
      final userJson = data['user'] as Map<String, dynamic>? ?? data['data'] as Map<String, dynamic>?;

      if (token == null) throw ApiException('Token missing in response');

      _token = token;
      await secureStorage.writeToken(token);

      if (userJson != null) {
        _user.value = UserModel.fromJson(userJson);
      } else {
        _user.value = await repository.fetchProfile();
      }

      Get.offAllNamed('/home');
    } on ApiException catch (e) {
      errorMessage.value = e.message;
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> logout() async {
    await repository.logout();
    _token = null;
    _user.value = null;
    await secureStorage.deleteToken();
    Get.offAllNamed('/login');
  }

  void handleUnauthorized() {
    _token = null;
    _user.value = null;
    secureStorage.deleteToken();
    Get.offAllNamed('/login');
  }
}
