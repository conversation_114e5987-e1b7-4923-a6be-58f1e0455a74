import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  final String id;

  @Json<PERSON>ey(name: 'first_name')
  final String firstName;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_name')
  final String lastName;

  final String phone;
  final String email;

  UserModel({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.email,
  });

  /// Factory to generate object from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  /// Convert object to JSON
  Map<String, dynamic> toJson() => _$UserModelToJson(this);
}
