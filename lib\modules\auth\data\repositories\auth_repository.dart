import '../../../../core/network/dio_client.dart';
import '../models/user_model.dart';

class AuthRepository {
  final DioClient dioClient;

  AuthRepository(this.dioClient);

  Future<Map<String, dynamic>> login(String email, String password) async {
    final response = await dioClient.dio.post('/owner/login', data: {
      'email': email,
      'password': password,
    });

    return response.data as Map<String, dynamic>;
  }

  Future<Map<String, dynamic>> register({
    required String firstName,
    required String lastName,
    required String email,
    required String phone,
    required String password,
    required String passwordConfirmation,
  }) async {
    final response = await dioClient.dio.post('/owner/register', data: {
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone': phone,
      'password': password,
      'password_confirmation': passwordConfirmation,
    });

    return response.data as Map<String, dynamic>;
  }

  Future<UserModel> fetchProfile() async {
    final response = await dioClient.dio.get('/owner/me');
    return UserModel.fromJson(response.data);
  }

  Future<void> logout() async {
    try {
      await dioClient.dio.post('/owner/logout');
    } catch (e) {
      // ignore errors
    }
  }
}