import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../shared/widgets/app_button.dart';
import '../../../shared/widgets/app_input.dart';
import '../controllers/auth_controller.dart';

class RegisterPage extends StatelessWidget {
  RegisterPage({super.key});

  final _formKey = GlobalKey<FormState>();
  final AuthController authController = Get.find<AuthController>();

  // Controllers for form fields
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final passwordController = TextEditingController();
  final passwordConfirmController = TextEditingController();

  final isLoading = false.obs;

  void _register() async {
    if (!_formKey.currentState!.validate()) return;

    isLoading.value = true;
    try {
      await authController.register(
        firstName: firstNameController.text.trim(),
        lastName: lastNameController.text.trim(),
        email: emailController.text.trim(),
        phone: phoneController.text.trim(),
        password: passwordController.text,
        passwordConfirmation: passwordConfirmController.text,
      );
      // Navigate to home or dashboard after successful registration
      Get.offAllNamed('/home');
    } catch (e) {
      Get.snackbar(
        "Registration Failed",
        e.toString(),
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoading.value = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Icon(
                  Icons.person_add_alt,
                  size: 80,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 16),
                Text(
                  "Create Account",
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.headlineMedium,
                ),
                const SizedBox(height: 32),

                // First Name
                AppInput(
                  controller: firstNameController,
                  label: "First Name",
                  hint: "Enter your first name",
                  prefixIcon: const Icon(Icons.person_outline),
                  validator: (val) =>
                      val == null || val.isEmpty ? "First name is required" : null,
                ),
                const SizedBox(height: 16),

                // Last Name
                AppInput(
                  controller: lastNameController,
                  label: "Last Name",
                  hint: "Enter your last name",
                  prefixIcon: const Icon(Icons.person_outline),
                  validator: (val) =>
                      val == null || val.isEmpty ? "Last name is required" : null,
                ),
                const SizedBox(height: 16),

                // Email
                AppInput(
                  controller: emailController,
                  label: "Email",
                  hint: "Enter your email",
                  keyboardType: TextInputType.emailAddress,
                  prefixIcon: const Icon(Icons.email_outlined),
                  validator: (val) {
                    if (val == null || val.isEmpty) {
                      return "Email is required";
                    }
                    if (!GetUtils.isEmail(val)) {
                      return "Enter a valid email";
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Phone
                AppInput(
                  controller: phoneController,
                  label: "Phone",
                  hint: "Enter your phone number",
                  keyboardType: TextInputType.phone,
                  prefixIcon: const Icon(Icons.phone_outlined),
                  validator: (val) =>
                      val == null || val.isEmpty ? "Phone number is required" : null,
                ),
                const SizedBox(height: 16),

                // Password
                AppInput(
                  controller: passwordController,
                  label: "Password",
                  obscureText: true,
                  prefixIcon: const Icon(Icons.lock_outline),
                  validator: (val) {
                    if (val == null || val.isEmpty) {
                      return "Password is required";
                    }
                    if (val.length < 6) {
                      return "Password must be at least 6 characters";
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Password Confirmation
                AppInput(
                  controller: passwordConfirmController,
                  label: "Confirm Password",
                  obscureText: true,
                  prefixIcon: const Icon(Icons.lock_outline),
                  validator: (val) {
                    if (val == null || val.isEmpty) {
                      return "Please confirm your password";
                    }
                    if (val != passwordController.text) {
                      return "Passwords do not match";
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),

                // Register Button
                Obx(() => AppButton(
                      label: "Sign Up",
                      isLoading: isLoading.value,
                      onPressed: _register,
                      icon: Icons.person_add,
                    )),

                const SizedBox(height: 16),

                // Already have account?
                TextButton(
                  onPressed: () {
                    Get.offAllNamed('/login');
                  },
                  child: const Text("Already have an account? Login"),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
