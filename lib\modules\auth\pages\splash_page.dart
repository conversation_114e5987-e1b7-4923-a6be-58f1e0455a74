import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class SplashPage extends StatelessWidget {
  const SplashPage({super.key});

  @override
  Widget build(BuildContext context) {
    Future.microtask(() async {
      await Future.delayed(const Duration(seconds: 4));

      final box = GetStorage();
      final completed = box.read('onboarding_complete') ?? false;

      if (!completed) {
        Get.offAllNamed('/onboarding/country');
      } else {
        Get.offAllNamed('/login');
      }
    });

    return const Scaffold(
      body: Center(child: CircularProgressIndicator()),
    );
  }
}
