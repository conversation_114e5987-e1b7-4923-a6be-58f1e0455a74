import 'package:flutter/material.dart';
// import '../../Auth/controllers/auth_controller.dart';
import 'package:get/get.dart';
import '../../../shared/widgets/text_field.dart';

class LoginForm extends StatefulWidget {
  final void Function(String email, String password) onSubmit;
  final bool isLoading;
  const LoginForm({super.key, required this.onSubmit, this.isLoading = false});

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final _formKey = GlobalKey<FormState>();
  final _email = TextEditingController();
  final _password = TextEditingController();

  @override
  void dispose() {
    _email.dispose();
    _password.dispose();
    super.dispose();
  }

  void _submit() {
    if (_formKey.currentState?.validate() ?? false) {
      widget.onSubmit(_email.text.trim(), _password.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _form<PERSON><PERSON>,
      child: Column(
        children: [
          AppTextField(
            label: 'Email',
            controller: _email,
            keyboardType: TextInputType.emailAddress,
            validator: (v) {
              if (v == null || v.isEmpty) return 'Email is required';
              if (!GetUtils.isEmail(v)) return 'Enter a valid email';
              return null;
            },
          ),
          const SizedBox(height: 12),
          AppTextField(
            label: 'Password',
            controller: _password,
            obscure: true,
            validator: (v) {
              if (v == null || v.isEmpty) return 'Password is required';
              if (v.length < 8) return 'Password must be at least 8 chars';
              return null;
            },
          ),
          const SizedBox(height: 20),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: widget.isLoading ? null : _submit,
              child: widget.isLoading ? const CircularProgressIndicator() : const Text('Login'),
            ),
          ),
        ],
      ),
    );
  }
}