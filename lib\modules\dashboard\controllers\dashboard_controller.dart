import 'package:get/get.dart';
import '../../auth/controllers/auth_controller.dart';

class DashboardController extends GetxController {
  // final AuthController _auth = Get.find<AuthController>();

  // Observables
  var currentTab = 0.obs; // For handling bottom nav / tab switching
  var isLoading = false.obs;

  // Example: store some fetched data for the home screen
  var welcomeMessage = "".obs;

  @override
  void onInit() {
    super.onInit();
    // _setWelcomeMessage();
  }

  // void _setWelcomeMessage() {
  //   final user = _auth.user;
  //   welcomeMessage.value =
  //       "Welcome back, ${user?.firstName ?? "Guest"} 👋";
  // }

  // Future<void> logout() async {
  //   await _auth.logout();
  // }
}
