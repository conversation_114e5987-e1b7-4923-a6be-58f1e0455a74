import 'package:flutter/material.dart';

import '../../../../shared/theme/app_colors.dart';
import '../../../../shared/widgets/progress_bar.dart';

class SystemHealth extends StatelessWidget {
  final bool fixedHeight;

  const SystemHealth({super.key, this.fixedHeight = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: fixedHeight ? 200 : null,
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: AppColors.lightGreen50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.lightGreen200),
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle_outline_rounded,
                color: Color(0xFF38a169),
                size: 30,
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "System Health",
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    "All systems operational",
                    style: TextStyle(fontSize: 12, color: AppColors.primary),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Text(
                "Overall Score:",
                style: TextStyle(fontSize: 12, color: AppColors.primary),
              ),
              const Spacer(),
              Text(
                "98/100",
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ProgressBar(value: 98 / 100, color: AppColors.primary),
        ],
      ),
    );
  }
}
