import 'package:flutter/material.dart';
import 'section_card.dart';

class ScrollableSection extends StatelessWidget {
  final String title;
  final IconData titleIcon;
  final List<Widget> items;
  final double? maxHeight;

  const ScrollableSection({
    super.key,
    required this.title,
    required this.titleIcon,
    required this.items,
    this.maxHeight,
  });

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;

    // Default responsive height
    final double responsiveHeight = (screenHeight * 0.35).clamp(250.0, 450.0);

    // Decide whether items fit without scroll
    final bool needsScroll = items.length > 4;

    return SectionCard(
      title: title,
      titleIcon: titleIcon,
      child: needsScroll
          ? SizedBox(
              height: maxHeight ?? responsiveHeight,
              child: ListView.builder(
                padding: EdgeInsets.zero,
                physics: const AlwaysScrollableScrollPhysics(),
                itemCount: items.length,
                itemBuilder: (context, index) => items[index],
              ),
            )
          : Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: items,
            ),
    );
  }
}
