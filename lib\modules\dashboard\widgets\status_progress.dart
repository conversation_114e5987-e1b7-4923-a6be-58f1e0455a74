import 'package:flutter/material.dart';

import '../../../shared/widgets/progress_bar.dart';

/// Reusable card for displaying status with progress
class StatusProgress extends StatelessWidget {
  final String title;
  final String subtitle;
  final double percentage; // 0–100

  const StatusProgress({
    super.key,
    this.subtitle = "",
    required this.title,
    required this.percentage,
  });

  /// Automatically determine status based on percentage
  (String statusText, IconData icon, Color color) _getStatus(double value) {
    if (value >= 80) {
      return (subtitle, Icons.check_circle_outline_rounded, Colors.green.shade700);
    } else if (value >= 50) {
      return (subtitle, Icons.error_outline, Colors.orange);
    } else {
      return (subtitle, Icons.warning_amber_rounded, Colors.red);
    }
  }

  @override
  Widget build(BuildContext context) {
    final (statusText, icon, color) = _getStatus(percentage);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title + Percentage
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            Text(
              "${percentage.toStringAsFixed(0)}%",
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),

        // Progress bar
        ProgressBar(
          value: percentage / 100,
          color: color,
        ),

        const SizedBox(height: 6),

        // Status row
        if (subtitle.isNotEmpty)
          Row(
            children: [
              Icon(icon, size: 14, color: color),
              const SizedBox(width: 4),
              Text(
                statusText,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
      ],
    );
  }
}