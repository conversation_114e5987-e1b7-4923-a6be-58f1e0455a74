import 'package:get/get.dart';
import '../../../core/network/dio_client.dart';
import '../../../features/device_report/controllers/device_report_controller.dart';
import '../../../features/device_report/data/repositories/device_report_repository.dart';
import '../controllers/binding_code_controller.dart';
import '../controllers/onboarding_controller.dart';
import '../controllers/version_controller.dart';
import '../data/repositories/app_version_repository.dart';
import '../data/repositories/binding_repository.dart';

class OnboardingBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut<AppVersionRepository>(() => AppVersionRepository(Get.find<DioClient>()));
    Get.lazyPut<OnboardingController>(() => OnboardingController(dioClient: Get.find<DioClient>()));
    Get.lazyPut<BindingRepository>(() => BindingRepository(Get.find<DioClient>()));
    Get.lazyPut<BindingCodeController>(() => BindingCodeController(Get.find<BindingRepository>(), Get.find<DeviceReportController>()));
    Get.lazyPut<VersionController>(() => VersionController(appVersionRepository: Get.find<AppVersionRepository>()));
    Get.lazyPut<DeviceReportRepository>(() => DeviceReportRepository(Get.find<DioClient>()));
    Get.lazyPut<DeviceReportController>(() => DeviceReportController(repository: Get.find<DeviceReportRepository>()));
  }
}