import 'package:get/get.dart';

import '../../../core/errors/error_handler.dart';
import '../../../features/device_report/controllers/device_report_controller.dart';
import '../../../shared/models/binding_code.dart';
import '../data/repositories/binding_repository.dart';

class BindingCodeController extends GetxController {
  final BindingRepository _bindingRepository;
  final DeviceReportController ctrl;

  BindingCodeController(this._bindingRepository, this.ctrl);

  var deviceType = ''.obs;
  var bindingCode = ''.obs;
  var deviceTypeError = ''.obs;
  var bindingCodeError = ''.obs;
  var isLoading = false.obs;

  var deviceTypes = <Map<String, dynamic>>[].obs;
  var deviceTypeNames = <String>[].obs;
  var isDeviceTypesLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    fetchDeviceTypes();
  }

  Future<void> fetchDeviceTypes() async {
    isDeviceTypesLoading.value = true;
    try {
      final types = await _bindingRepository.getDeviceTypes();
      deviceTypes.assignAll(types);
      deviceTypeNames.assignAll(types.map((e) => e["name"].toString()));
    } catch (e) {
      Get.snackbar("Error", "Could not load device types: $e");
    } finally {
      isDeviceTypesLoading.value = false;
    }
  }

  void setDeviceType(String value) {
    deviceType.value = value;
  }

  void setBindingCode(String value) {
    bindingCode.value = value.toUpperCase();
  }

  Future<void> submit() async {
    // Clear previous errors
    deviceTypeError.value = '';
    bindingCodeError.value = '';
    
    bool isValid = true;
    
    if (deviceType.isEmpty) {
      deviceTypeError.value = 'Please select a device type';
      isValid = false;
    }
    
    if (bindingCode.value.length != 8) {
      bindingCodeError.value = 'Binding code must be exactly 8 characters';
      isValid = false;
    }
    
    if (!isValid) return;

    isLoading.value = true;
    
    try {
      ctrl.submitReport('');
      final BindingCode binding = await _bindingRepository.validateBindingCode(
        bindingCode: bindingCode.value,
        deviceType: deviceType.value,
      );

      
      Get.toNamed(
        '/binding/confirmation',
        arguments: {
          'schoolName': binding.school?.name,
          'schoolLocation': "${binding.school?.address}, ${binding.school?.localGovernmentArea?.name} LGA",
          'bindingCode': binding.code,
          'deviceType': binding.deviceType,
          'status': 'Active',
        },
      );
    } catch (e) {
      ErrorHandler.handle(e);
      // ErrorHandler.handle(e, retry: submit);
    } finally {
      isLoading.value = false;
    }
  }
}
