import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../../../core/network/dio_client.dart';
import '../data/models/system_check.dart';

class OnboardingController extends GetxController {
  final _storage = GetStorage();
  final DioClient dioClient;

  OnboardingController({required this.dioClient});

  var checks = <SystemCheck>[
    SystemCheck(id: "network", label: "Network Connection", icon: "wifi"),
    SystemCheck(id: "hardware", label: "Hardware Verification", icon: "computer"),
    SystemCheck(id: "database", label: "Database Connection", icon: "storage"),
    SystemCheck(id: "security", label: "Security Protocols", icon: "shield"),
  ].obs;

  // toggle for testing
  bool get isOnboardingComplete => _storage.read('onboarding_complete') ?? false;

  @override
  void onInit() {
    super.onInit();
    _runChecks();
  }

  Future<void> _runChecks() async {
    for (int i = 0; i < checks.length; i++) {
      // Mark current as checking
      checks[i].status = CheckStatus.checking;
      checks.refresh();

      bool success = false;

      switch (checks[i].id) {
        case "network":
          success = await _checkNetwork();
          break;
        case "database":
          success = await _checkDatabase();
          break;
        case "hardware":
          success = await _checkHardware();
          break;
        case "security":
          success = await _checkSecurity();
          break;
      }

      checks[i].status = success ? CheckStatus.completed : CheckStatus.failed;
      checks.refresh();
    }

    // Navigate after short delay
    await Future.delayed(const Duration(seconds: 2));
    completeOnboarding();
  }

  /// 🔹 Check internet connectivity
  Future<bool> _checkNetwork() async {
    final results = await (Connectivity().checkConnectivity());
    return results.isNotEmpty && !results.contains(ConnectivityResult.none);
  }

  /// 🔹 Check database connection
  Future<bool> _checkDatabase() async {
    try {
      await dioClient.dio.get('/app/health');
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 🔹 Check hardware verification
  Future<bool> _checkHardware() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return true;
  }

  /// 🔹 Check security protocols
  Future<bool> _checkSecurity() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return true;
  }

  IconData getIcon(String iconName) {
    switch (iconName) {
      case "wifi":
        return Icons.wifi;
      case "computer":
        return Icons.computer;
      case "storage":
        return Icons.storage;
      case "shield":
        return Icons.shield;
      default:
        return Icons.check_circle;
    }
  }

  void completeOnboarding() {
    // _storage.write('onboarding_complete', true);
    Get.offAllNamed('/binding');
  }

  void resetOnboarding() {
    // For testing
    _storage.remove('onboarding_complete');
  }
}
