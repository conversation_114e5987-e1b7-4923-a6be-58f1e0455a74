import 'package:get/get.dart';

import '../data/models/app_version_response.dart';
import '../data/repositories/app_version_repository.dart';

class VersionController extends GetxController {
  final AppVersionRepository appVersionRepository;

  VersionController({required this.appVersionRepository});

  final Rxn<AppVersionResponse> appVersion = Rxn<AppVersionResponse>();
  final RxBool isLoading = false.obs;
  final RxnString errorMessage = RxnString();

  Future<void> checkAppVersion() async {
    isLoading.value = true;
    errorMessage.value = null;

    try {
      final response = await appVersionRepository.checkAppVersion();
      appVersion.value = response;
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }
}

