class AppVersionResponse {
  final String latestVersion;
  final String buildNumber;
  final bool mustUpdate;
  final bool forceUpdate;
  final String updateUrl;
  final String changelog;

  AppVersionResponse({
    required this.latestVersion,
    required this.buildNumber,
    required this.mustUpdate,
    required this.forceUpdate,
    required this.updateUrl,
    required this.changelog,
  });

  factory AppVersionResponse.fromJson(Map<String, dynamic> json) {
    return AppVersionResponse(
      latestVersion: json['latestVersion'] ?? '',
      buildNumber: json['buildNumber'] ?? '',
      mustUpdate: json['mustUpdate'] ?? false,
      forceUpdate: json['forceUpdate'] ?? false,
      updateUrl: json['updateUrl'] ?? '',
      changelog: json['changelog'] ?? '',
    );
  }
}
