import 'package:dio/dio.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:get/get.dart';

import '../../../../core/network/dio_client.dart';
import '../models/app_version_response.dart';

class AppVersionRepository {
  final DioClient dioClient;
  
  AppVersionRepository(this.dioClient);

  Future<AppVersionResponse> checkAppVersion() async {
    final info = await PackageInfo.fromPlatform();
    final response = await dioClient.dio.get('/app/version', options: Options(headers: {
      "X-Platform": GetPlatform.isAndroid ? "android" : "ios",
      "X-App-Version": info.version,
    }));
    return AppVersionResponse.fromJson(response.data);
  }
}
