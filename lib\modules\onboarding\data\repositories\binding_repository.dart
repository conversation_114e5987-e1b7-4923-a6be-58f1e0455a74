import 'package:dio/dio.dart';

import '../../../../core/errors/app_exception.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../shared/models/binding_code.dart';

class BindingRepository {
  final DioClient dioClient;

  BindingRepository(this.dioClient);

  Future<List<Map<String, dynamic>>> getDeviceTypes() async {
    final response = await dioClient.dio.get("/core/device-types");
    
    if (response.statusCode == 200 && response.data["DATA"] != null) {
      // Ensure list of maps
      final List data = response.data["DATA"];
      return data.map((e) => e as Map<String, dynamic>).toList();
    } else {
      throw Exception("Invalid response from server");
    }
  }

  Future<BindingCode> validateBindingCode({
    required String bindingCode,
    required String deviceType,
  }) async {
    try {
      final response = await dioClient.dio.post(
        "/app/binding/validate",
        data: {
          "binding_code": bindingCode,
          "device_type": deviceType,
        },
      );

      if (response.data == null || response.data["DATA"] == null) {
        throw ServerException("Invalid response from server");
      }

      return BindingCode.fromJson(response.data["DATA"]);
    } on DioException catch (e) {
      throw e.error as AppException;
    }
  }
}
