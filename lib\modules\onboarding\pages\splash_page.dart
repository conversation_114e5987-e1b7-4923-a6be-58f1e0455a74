import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../shared/theme/app_colors.dart';
import '../../../shared/widgets/watermark.dart';
import '../controllers/onboarding_controller.dart';
import '../controllers/version_controller.dart';
import '../data/models/system_check.dart';

class SplashPage extends StatelessWidget {
  SplashPage({super.key});

  final OnboardingController controller = Get.find<OnboardingController>();
  final VersionController versionController = Get.find<VersionController>();

  final RxBool isLoading = false.obs;

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.primary,
              AppColors.primary.withValues(alpha: 0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Stack(
          children: [
            Center(
              child: Watermark(
                child: <PERSON>umn(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      'assets/images/logo.png',
                      width: 96,
                      height: 96,
                    ),
                
                    const Text(
                      "Smart School Monitor",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 6),
                    const Text(
                      "Enugu State Government",
                      style: TextStyle(color: AppColors.secondary, fontSize: 18),
                    ),
                    const SizedBox(height: 6),
                    const Text(
                      "Educational Excellence Initiative",
                      style: TextStyle(color: AppColors.accent, fontSize: 16),
                    ),
                    const SizedBox(height: 32),
                
                    // System Checks Card
                    Container(
                      width: 350,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.95),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 10,
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          const Text(
                            "System Initialization",
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 18,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Obx(
                            () => Column(
                              children: controller.checks.map((check) {
                                return Container(
                                  margin: const EdgeInsets.only(bottom: 12),
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        controller.getIcon(check.icon),
                                        color: Colors.black54,
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Text(
                                          check.label,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                      if (check.status == CheckStatus.pending)
                                        Container(
                                          width: 22,
                                          height: 22,
                                          decoration: BoxDecoration(
                                            border: Border.all(
                                              color: Colors.grey,
                                              width: 2,
                                            ),
                                            shape: BoxShape.circle,
                                          ),
                                        )
                                      else if (check.status ==
                                          CheckStatus.checking)
                                        const SizedBox(
                                          width: 22,
                                          height: 22,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: AppColors.primary,
                                          ),
                                        )
                                      else if (check.status == CheckStatus.completed)
                                        const Icon(
                                          Icons.check_circle,
                                          color: AppColors.primary,
                                        )
                                      else if (check.status == CheckStatus.failed)
                                        const Icon(
                                          Icons.error,
                                          color: Colors.red,
                                        ),
                                    ],
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
