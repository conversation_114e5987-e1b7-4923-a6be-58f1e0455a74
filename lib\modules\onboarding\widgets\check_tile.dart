import 'package:flutter/material.dart';
import '../data/models/system_check.dart';

class CheckTile extends StatelessWidget {
  final SystemCheck check;
  final IconData Function(String) getIcon;

  const CheckTile({super.key, required this.check, required this.getIcon});

  @override
  Widget build(BuildContext context) {
    Widget trailing;

    switch (check.status) {
      case CheckStatus.checking:
        trailing = const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        );
        break;
      case CheckStatus.completed:
        trailing = const Icon(Icons.check_circle, color: Colors.green);
        break;
      case CheckStatus.failed:
        trailing = const Icon(Icons.error, color: Colors.red);
        break;
      default:
        trailing = const Icon(Icons.hourglass_empty, color: Colors.grey);
    }

    return ListTile(
      leading: Icon(getIcon(check.icon)),
      title: Text(check.label),
      trailing: trailing,
    );
  }
}
