import 'package:json_annotation/json_annotation.dart';
import 'school.dart';

part 'binding_code.g.dart';

@JsonSerializable()
class BindingCode {
  final int id;
  final School? school;
  final String deviceType;
  final String code;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'expiresAt')
  final DateTime expiresAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'createdAt')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updatedAt')
  final DateTime updatedAt;

  const BindingCode({
    required this.id,
    this.school,
    required this.deviceType,
    required this.code,
    required this.expiresAt,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Connect the generated [_$BindingCodeFromJson] function to the `fromJson`
  /// factory.
  factory BindingCode.fromJson(Map<String, dynamic> json) => 
      _$BindingCodeFromJson(json);

  /// Connect the generated [_$BindingCodeToJson] function to the `toJson` method.
  Map<String, dynamic> toJson() => _$BindingCodeToJson(this);

  @override
  String toString() {
    return 'BindingCode{id: $id, code: $code, deviceType: $deviceType}';
  }

  /// Creates a copy of the BindingCode with the given fields replaced by the non-null values.
  BindingCode copyWith({
    int? id,
    School? school,
    String? deviceType,
    String? code,
    DateTime? expiresAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BindingCode(
      id: id ?? this.id,
      school: school ?? this.school,
      deviceType: deviceType ?? this.deviceType,
      code: code ?? this.code,
      expiresAt: expiresAt ?? this.expiresAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}