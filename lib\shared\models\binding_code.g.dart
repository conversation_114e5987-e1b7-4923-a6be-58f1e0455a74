// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'binding_code.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BindingCode _$BindingCodeFromJson(Map<String, dynamic> json) => BindingCode(
  id: (json['id'] as num).toInt(),
  school: json['school'] == null
      ? null
      : School.fromJson(json['school'] as Map<String, dynamic>),
  deviceType: json['deviceType'] as String,
  code: json['code'] as String,
  expiresAt: DateTime.parse(json['expiresAt'] as String),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$BindingCodeToJson(BindingCode instance) =>
    <String, dynamic>{
      'id': instance.id,
      'school': instance.school,
      'deviceType': instance.deviceType,
      'code': instance.code,
      'expiresAt': instance.expiresAt.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
