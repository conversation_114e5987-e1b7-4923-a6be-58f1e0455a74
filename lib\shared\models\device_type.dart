import 'package:json_annotation/json_annotation.dart';

part 'device_type.g.dart';

@JsonSerializable()
class DeviceType {
  final int id;
  final String name;
  final String status;
  @JsonKey(name: 'createdAt')
  final String createdAt;
  @JsonKey(name: 'updatedAt')
  final String updatedAt;

  const DeviceType({
    required this.id,
    required this.name,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Connect the generated [_$DeviceTypeFromJson] function to the `fromJson`
  /// factory.
  factory DeviceType.fromJson(Map<String, dynamic> json) => 
      _$DeviceTypeFromJson(json);

  /// Connect the generated [_$DeviceTypeToJson] function to the `toJson` method.
  Map<String, dynamic> toJson() => _$DeviceTypeToJson(this);

  @override
  String toString() {
    return 'DeviceType{id: $id, name: $name, status: $status}';
  }

  /// Creates a copy of the DeviceType with the given fields replaced by the non-null values.
  DeviceType copyWith({
    int? id,
    String? name,
    String? status,
    String? createdAt,
    String? updatedAt,
  }) {
    return DeviceType(
      id: id ?? this.id,
      name: name ?? this.name,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}