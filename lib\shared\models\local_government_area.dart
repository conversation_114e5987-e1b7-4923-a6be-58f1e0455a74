import 'package:json_annotation/json_annotation.dart';

part 'local_government_area.g.dart';

@JsonSerializable()
class LocalGovernmentArea {
  final int id;
  final String name;
  final String status;
  @Json<PERSON>ey(name: 'createdAt')
  final String createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updatedAt')
  final String updatedAt;

  const LocalGovernmentArea({
    required this.id,
    required this.name,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Connect the generated [_$LocalGovernmentAreaFromJson] function to the `fromJson`
  /// factory.
  factory LocalGovernmentArea.fromJson(Map<String, dynamic> json) => 
      _$LocalGovernmentAreaFromJson(json);

  /// Connect the generated [_$LocalGovernmentAreaToJson] function to the `toJson` method.
  Map<String, dynamic> toJson() => _$LocalGovernmentAreaToJson(this);

  @override
  String toString() {
    return 'LocalGovernmentArea{id: $id, name: $name, status: $status}';
  }

  /// Creates a copy of the LocalGovernmentArea with the given fields replaced by the non-null values.
  LocalGovernmentArea copyWith({
    int? id,
    String? name,
    String? status,
    String? createdAt,
    String? updatedAt,
  }) {
    return LocalGovernmentArea(
      id: id ?? this.id,
      name: name ?? this.name,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}