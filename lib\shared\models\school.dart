import 'package:json_annotation/json_annotation.dart';
import 'local_government_area.dart';
import 'school_type.dart';
import 'binding_code.dart';

part 'school.g.dart';

@JsonSerializable()
class School {
  final int id;
  @Json<PERSON>ey(name: 'localGovernmentArea')
  final LocalGovernmentArea? localGovernmentArea;
  final SchoolType? type;
  final String name;
  final String principal;
  final int students;
  final int staff;
  @JsonKey(name: 'establishedAt')
  final DateTime? establishedAt;
  final String address;
  final String phone;
  final String email;
  final String description;
  final String latitude;
  final String longitude;
  final String status;
  @JsonKey(name: 'binding_codes')
  final List<BindingCode?> bindingCodes;
  @JsonKey(name: 'createdAt')
  final DateTime createdAt;
  @Json<PERSON><PERSON>(name: 'updatedAt')
  final DateTime updatedAt;

  const School({
    required this.id,
    this.localGovernmentArea,
    this.type,
    required this.name,
    required this.principal,
    required this.students,
    required this.staff,
    this.establishedAt,
    required this.address,
    required this.phone,
    required this.email,
    required this.description,
    required this.latitude,
    required this.longitude,
    required this.status,
    this.bindingCodes = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  /// Connect the generated [_$SchoolFromJson] function to the `fromJson`
  /// factory.
  factory School.fromJson(Map<String, dynamic> json) => _$SchoolFromJson(json);

  /// Connect the generated [_$SchoolToJson] function to the `toJson` method.
  Map<String, dynamic> toJson() => _$SchoolToJson(this);

  @override
  String toString() {
    return 'School{id: $id, name: $name, principal: $principal, status: $status}';
  }

  /// Creates a copy of the School with the given fields replaced by the non-null values.
  School copyWith({
    int? id,
    LocalGovernmentArea? localGovernmentAreaId,
    SchoolType? type,
    String? name,
    String? principal,
    int? students,
    int? staff,
    DateTime? establishedAt,
    String? address,
    String? phone,
    String? email,
    String? description,
    String? latitude,
    String? longitude,
    String? status,
    List<BindingCode?>? bindingCodes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return School(
      id: id ?? this.id,
      localGovernmentArea: localGovernmentArea ?? this.localGovernmentArea,
      type: type ?? this.type,
      name: name ?? this.name,
      principal: principal ?? this.principal,
      students: students ?? this.students,
      staff: staff ?? this.staff,
      establishedAt: establishedAt ?? this.establishedAt,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      description: description ?? this.description,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      status: status ?? this.status,
      bindingCodes: bindingCodes ?? this.bindingCodes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}