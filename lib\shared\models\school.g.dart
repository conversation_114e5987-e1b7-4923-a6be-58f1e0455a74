// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'school.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

School _$SchoolFromJson(Map<String, dynamic> json) => School(
  id: (json['id'] as num).toInt(),
  localGovernmentArea: json['localGovernmentArea'] == null
      ? null
      : LocalGovernmentArea.fromJson(
          json['localGovernmentArea'] as Map<String, dynamic>,
        ),
  type: json['type'] == null
      ? null
      : SchoolType.fromJson(json['type'] as Map<String, dynamic>),
  name: json['name'] as String,
  principal: json['principal'] as String,
  students: (json['students'] as num).toInt(),
  staff: (json['staff'] as num).toInt(),
  establishedAt: json['establishedAt'] == null
      ? null
      : DateTime.parse(json['establishedAt'] as String),
  address: json['address'] as String,
  phone: json['phone'] as String,
  email: json['email'] as String,
  description: json['description'] as String,
  latitude: json['latitude'] as String,
  longitude: json['longitude'] as String,
  status: json['status'] as String,
  bindingCodes:
      (json['binding_codes'] as List<dynamic>?)
          ?.map(
            (e) => e == null
                ? null
                : BindingCode.fromJson(e as Map<String, dynamic>),
          )
          .toList() ??
      const [],
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$SchoolToJson(School instance) => <String, dynamic>{
  'id': instance.id,
  'localGovernmentArea': instance.localGovernmentArea,
  'type': instance.type,
  'name': instance.name,
  'principal': instance.principal,
  'students': instance.students,
  'staff': instance.staff,
  'establishedAt': instance.establishedAt?.toIso8601String(),
  'address': instance.address,
  'phone': instance.phone,
  'email': instance.email,
  'description': instance.description,
  'latitude': instance.latitude,
  'longitude': instance.longitude,
  'status': instance.status,
  'binding_codes': instance.bindingCodes,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};
