import 'package:json_annotation/json_annotation.dart';

part 'school_type.g.dart';

@JsonSerializable()
class SchoolType {
  final int id;
  final String name;
  final String status;
  @JsonKey(name: 'createdAt')
  final String createdAt;
  @JsonKey(name: 'updatedAt')
  final String updatedAt;

  const SchoolType({
    required this.id,
    required this.name,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Connect the generated [_$SchoolTypeFromJson] function to the `fromJson`
  /// factory.
  factory SchoolType.fromJson(Map<String, dynamic> json) => 
      _$SchoolTypeFromJson(json);

  /// Connect the generated [_$SchoolTypeToJson] function to the `toJson` method.
  Map<String, dynamic> toJson() => _$SchoolTypeToJson(this);

  @override
  String toString() {
    return 'SchoolType{id: $id, name: $name, status: $status}';
  }

  /// Creates a copy of the SchoolType with the given fields replaced by the non-null values.
  SchoolType copyWith({
    int? id,
    String? name,
    String? status,
    String? createdAt,
    String? updatedAt,
  }) {
    return SchoolType(
      id: id ?? this.id,
      name: name ?? this.name,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}