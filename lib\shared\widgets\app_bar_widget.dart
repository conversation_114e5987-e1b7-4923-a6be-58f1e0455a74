import 'package:flutter/material.dart';

import '../theme/app_colors.dart';

class AppBarWidget extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final bool showBackButton;
  final VoidCallback? onBack;

  const AppBarWidget({
    super.key,
    required this.title,
    this.showBackButton = false,
    this.onBack,
  });

  @override
  Size get preferredSize => const Size.fromHeight(72);

  @override
  Widget build(BuildContext context) {
    final isLarge = MediaQuery.of(context).size.width > 500;

    return Material(
      color: AppColors.primary,
      elevation: 4,
      child: SafeArea(
        bottom: false,
        child: Container(
          height: preferredSize.height,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              // Back button (if enabled)
              if (showBackButton) ...[
                IconButton(
                  onPressed: onBack ?? () => Navigator.of(context).maybePop(),
                  icon: const Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 24,
                  ),
                  tooltip: 'Back',
                ),
                const SizedBox(width: 8),
              ],

              // Logo
              Image.asset('assets/images/logo.png', width: 40, height: 40),
              const SizedBox(width: 6),

              // App name + subtitle (only on large screens)
              if (isLarge)
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      Text(
                        "Smart School Monitor",
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w700,
                          fontSize: 18,
                          height: 1.1,
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        "Enugu State Government",
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w400,
                          fontSize: 12,
                          height: 1.1,
                        ),
                      ),
                    ],
                  ),
                )
              else
                const Spacer(),

              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
