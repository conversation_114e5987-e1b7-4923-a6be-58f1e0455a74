import 'package:flutter/material.dart';

/// Reusable progress bar widget
class ProgressBar extends StatelessWidget {
  final double value; // between 0 and 1
  final Color color;
  final double height;
  final BorderRadiusGeometry borderRadius;

  const ProgressBar({
    super.key,
    required this.value,
    required this.color,
    this.height = 8,
    this.borderRadius = const BorderRadius.all(Radius.circular(10)),
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius,
      child: LinearProgressIndicator(
        value: value,
        minHeight: height,
        valueColor: AlwaysStoppedAnimation<Color>(color),
        backgroundColor: Colors.grey.shade300,
      ),
    );
  }
}
