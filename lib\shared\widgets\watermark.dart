import 'package:flutter/material.dart';

import '../theme/app_colors.dart';

class Watermark extends StatelessWidget {
  final Widget child;
  final String assetPath;
  final double opacity;
  final double scale;

  const Watermark({
    super.key,
    required this.child,
    this.assetPath = 'assets/images/logo.png',
    this.opacity = 0.08,
    this.scale = 1.5,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Fixed background watermark
        Positioned.fill(
          child: Center(
            child: Opacity(
              opacity: opacity,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Transform.scale(
                    scale: scale,
                    child: Image.asset(
                      assetPath,
                      fit: BoxFit.contain,
                      width: 200,
                    ),
                  ),
                  const SizedBox(height: 38),
                  const Text(
                    "Smart School Monitor",
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        // Foreground scrollable content
        child,
      ],
    );
  }
}
